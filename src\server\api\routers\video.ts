import {
  DeleteObjectCommand,
  GetObjectCommand,
  ListObjectsV2Command,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import Mux from "@mux/mux-node";
import { createId } from "@paralleldrive/cuid2";
import { TRPCError } from "@trpc/server";
import { and, eq, inArray, sql } from "drizzle-orm";
import { z } from "zod";
import { env } from "~/env";
import { SortBy, UserRole, sortBy, sports, videoStatuses } from "~/lib/enums";
import { competitionFormSchema } from "~/lib/formSchemas";
import { getUserSports } from "~/lib/roles";
import {
  createTRPCRouter,
  protectedProcedure,
  publicProcedure,
} from "~/server/api/trpc";
import {
  competition,
  videoAthletes,
  videoTags,
  videos,
} from "~/server/db/schema";
import { convertVideo } from "~/server/utils/aws";
import { getCloudFrontSignedUrl } from "~/server/utils/cloudFront";
import { refreshMUXToken } from "~/server/utils/mux";
import { checkSport, checkVideoPermission } from "~/server/utils/permissions";
import {
  getVideos,
  updateManyAthletes,
  updateManyCompetitions,
  updateManyTags,
  updateManyVideos,
  updateVideoAthletes,
  updateVideoTags,
} from "~/server/utils/videos";
import type { RouterInputs, RouterOutputs } from "../root";
import { s3Client } from "./aws";
import { getAthletesAttr, getHpAthleteCached } from "~/server/utils/pta";

type Outputs = RouterOutputs["video"];
type Inputs = RouterInputs["video"];

export type VideoAllOutput = Outputs["all"];
export type VideoUpdateInput = Inputs["upsert"];

export type VideoUpdateManyInput = Inputs["updateMany"];

export const videoRouter = createTRPCRouter({
  all: protectedProcedure
    .input(
      z.object({
        page: z.number().default(0),
        pageSize: z.number().default(12),
        searchText: z.string().optional(),
        sports: z.enum(sports).array().optional(),
        videoStatuses: z.enum(videoStatuses).array().optional(),
        metadata: z.array(z.string()).optional(),
        sortBy: z
          .enum([...sortBy, "name_desc", "video_date_asc"])
          .optional()
          .default(SortBy.video_date),
      }),
    )
    .query(async ({ ctx, input }) => {
      const {
        page,
        pageSize,
        searchText,
        sports,
        videoStatuses,
        metadata,
        sortBy,
      } = input;

      // Regular video listing logic - now only shows parent videos with linked video counts
      const isAdmin = ctx.session.user.roles.includes(UserRole.admin);
      const userSports = getUserSports(ctx.session.user.roles as UserRole[]);

      return getVideos({
        db: ctx.db,
        isAdmin,
        userSports,
        page: Math.max(0, page),
        pageSize,
        searchText,
        sports,
        videoStatuses,
        metadata,
        subscribedAthletesId: ctx.session.user.athletes,
        sortBy,
      });
    }),
  get: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const video = await ctx.db.query.videos.findFirst({
        where: (fields) =>
          and(
            eq(fields.id, input.id),
            eq(fields.isDeleted, false),
            eq(fields.isDraft, false),
          ),
        with: {
          athletes: true,
          competition: true,
          tags: true,
        },
      });
      if (
        !video?.fullFilename ||
        !video.sport ||
        !video.athletes ||
        !video.muxAssetId ||
        !video.muxPlaybackId
      ) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Video not found",
        });
      }

      //check permissions
      checkSport(ctx, video.sport);
      const hpAthletes = await getHpAthleteCached();
      const hpAthleteIds = hpAthletes.map((x) => x.athlete_id);
      const videoAthletesIds = video.athletes
        .filter((x) => hpAthleteIds.includes(x.athleteId))
        .map((x) => x.athleteId);
      await checkVideoPermission(ctx, video.permission, videoAthletesIds);

      //generate token

      const newPlaybackToken = await refreshMUXToken({
        aud: "video",
        muxPlaybackId: video.muxPlaybackId,
        oldToken: video.playbackToken,
      });
      const newThumbnailToken = await refreshMUXToken({
        aud: "thumbnail",
        muxPlaybackId: video.muxPlaybackId,
        oldToken: video.thumbnailToken,
        thumbnailUrl: video.thumbnail,
      });
      const newStoryboardToken = await refreshMUXToken({
        aud: "storyboard",
        muxPlaybackId: video.muxPlaybackId,
        oldToken: video.storyboardToken,
      });

      const thumbnailToken =
        newThumbnailToken.newToken ?? newThumbnailToken.oldToken;
      const playbackToken =
        newPlaybackToken.newToken ?? newPlaybackToken.oldToken;
      const storyboardToken =
        newStoryboardToken.newToken ?? newStoryboardToken.oldToken;

      let duration: number | null | undefined = video.duration;
      let maxHeight: number | null | undefined = video.maxHeight;
      let maxWidth: number | null | undefined = video.maxWidth;
      if (duration === 0) {
        const mux = new Mux({
          tokenId: env.MUX_ACCESS_TOKEN_ID,
          tokenSecret: env.MUX_SECRET_KEY,
        });
        const asset = await mux.video.assets.retrieve(video.muxAssetId);
        duration = asset.duration;
        const videoTrack = asset.tracks?.find((x) => x.type === "video");
        if (videoTrack) {
          maxHeight = videoTrack.max_height;
          maxWidth = videoTrack.max_width;
        }
      }

      if (
        !!newPlaybackToken.newToken ||
        !!newThumbnailToken.newToken ||
        !!newStoryboardToken.newToken ||
        duration !== video.duration ||
        maxHeight !== video.maxHeight ||
        maxWidth !== video.maxWidth
      ) {
        await ctx.db
          .update(videos)
          .set({
            playbackToken,
            thumbnailToken,
            storyboardToken,
            duration,
            maxHeight,
            maxWidth,
          })
          .where(eq(videos.id, video.id));
      }
      video.playbackToken = playbackToken;
      video.thumbnailToken = thumbnailToken;
      video.storyboardToken = storyboardToken;

      const athletesWithAttr = await getAthletesAttr(video.athletes);

      return {
        ...video,
        athletes: video.athletes.map((athlete) => {
          const athleteWithAttr = athletesWithAttr.find(
            (x) => x.athleteId === athlete.athleteId,
          );
          return {
            ...athlete,
            name: athleteWithAttr?.name ?? "",
            isHp: athleteWithAttr?.isHp ?? false,
          };
        }),
      };
    }),
  createMUXAsset: protectedProcedure
    .input(
      z.object({
        fullFilename: z.string(),
        filename: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      const { fullFilename, filename } = input;
      const getObjectCommand = new GetObjectCommand({
        Bucket: env.AWS_BUCKET_NAME,
        Key: fullFilename,
      });
      const videoPresignedUrl = await getSignedUrl(s3Client, getObjectCommand, {
        expiresIn: 3600 * 24, // URL expires in 1 day
      });

      const mux = new Mux({
        tokenId: env.MUX_ACCESS_TOKEN_ID,
        tokenSecret: env.MUX_SECRET_KEY,
      });
      const asset = await mux.video.assets.create({
        input: [{ url: videoPresignedUrl }],
        playback_policy: ["signed"],
        max_resolution_tier: "2160p",
      });

      const playbackId = asset.playback_ids?.[0]?.id;

      if (!playbackId) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create asset",
        });
      }
      const { newToken: thumbnailToken } = await refreshMUXToken({
        aud: "thumbnail",
        muxPlaybackId: playbackId,
      });
      const { newToken: playbackToken } = await refreshMUXToken({
        aud: "video",
        muxPlaybackId: playbackId,
      });

      const { newToken: storyboardToken } = await refreshMUXToken({
        aud: "storyboard",
        muxPlaybackId: playbackId,
      });

      return {
        playbackId,
        thumbnailToken: thumbnailToken!,
        playbackToken: playbackToken!,
        storyboardToken: storyboardToken!,
        filename,
        fullFilename,
        muxAssetId: asset.id,
      };
    }),

  publish: protectedProcedure
    .input(z.object({ videoIds: z.array(z.string()), isDraft: z.boolean() }))
    .mutation(async ({ ctx, input }) => {
      const isAdmin = ctx.session.user.roles.includes("admin");
      const isAnalyst = ctx.session.user.roles.includes("analyst");

      const andCondition = [inArray(videos.id, input.videoIds)];
      if (!isAdmin) {
        if (isAnalyst) {
          //analyst can view videos in his sports
          const analystSports = getUserSports(
            ctx.session.user.roles as UserRole[],
          );
          if (analystSports.length > 0) {
            andCondition.push(inArray(videos.sport, analystSports));
          } else {
            andCondition.push(eq(videos.createdById, ctx.session.user.id));
          }
        } else {
          andCondition.push(eq(videos.createdById, ctx.session.user.id));
        }
      }

      await ctx.db
        .update(videos)
        .set({
          isDraft: input.isDraft,
        })
        .where(and(...andCondition));
    }),
  upsert: protectedProcedure
    .input(
      z.object({
        id: z.string().optional(),
        title: z.string().min(1).optional(),
        thumbnail: z.string().optional(),
        sport: z.enum(videos.sport.enumValues).optional(),
        permission: z.enum(videos.permission.enumValues).optional(),
        status: z.enum(videos.status.enumValues).optional(),
        muxAssetId: z.string().optional(),
        muxPlaybackId: z.string().optional(),
        duration: z.number().optional().default(0),
        maxHeight: z.number().optional().default(0),
        maxWidth: z.number().optional().default(0),
        filename: z.string().optional(),
        fullFilename: z.string().optional(),
        thumbnailToken: z.string().optional(),
        playbackToken: z.string().optional(),
        storyboardToken: z.string().optional(),
        athletes: z
          .array(
            z.object({
              athleteId: z.string(),
              isHp: z.boolean(),
              name: z.string(),
            }),
          )
          .optional(),
        tags: z.array(z.string()).optional(),
        competition: competitionFormSchema.optional(),
        newVideo: z.boolean().optional().default(false),
        videoDate: z.date().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const isNew = !input.id;

      // If this is an update (not a new video), check if the video is a child video
      let isChildVideo = false;
      if (!isNew && input.id) {
        const existingVideo = await ctx.db.query.videos.findFirst({
          where: (fields) => eq(fields.id, input.id!),
          columns: { parentId: true, sport: true },
        });

        isChildVideo = !!existingVideo?.parentId;

        if (isChildVideo) {
          // For child videos, only allow editing of title and tags
          const allowedFields = ["id", "title", "tags"];
          const providedFields = Object.keys(input).filter(
            (key) => input[key as keyof typeof input] !== undefined,
          );
          const disallowedFields = providedFields.filter(
            (field) => !allowedFields.includes(field),
          );

          if (disallowedFields.length > 0) {
            throw new TRPCError({
              code: "FORBIDDEN",
              message: `Cannot edit these fields on child videos: ${disallowedFields.join(", ")}. Child videos inherit competition, sport, permission, and athletes from their parent video. Only title and tags can be edited independently.`,
            });
          }

          // Check sport permissions for child video
          if (existingVideo?.sport) {
            checkSport(ctx, existingVideo.sport);
          }
        }
      }

      const {
        id: inputId,
        athletes,
        tags,
        competition: inputCompetition,
        newVideo,
        ...rest
      } = input;

      const id = inputId ?? createId();

      if (isChildVideo) {
        // For child videos, only update allowed fields
        const updateData: Partial<typeof rest> = {};
        if (input.title !== undefined) {
          updateData.title = input.title;
        }

        if (Object.keys(updateData).length > 0) {
          await ctx.db.update(videos).set(updateData).where(eq(videos.id, id));
        }

        // Update tags for child videos
        if (tags !== undefined) {
          await updateVideoTags(ctx.db, false, id, tags);
        }
      } else {
        // For parent videos or new videos, use the original logic
        await ctx.db
          .insert(videos)
          .values({
            ...rest,
            id,
            isDraft: isNew ? false : undefined,
            createdById: ctx.session.user.id,
            competitionId: inputCompetition?.id,
          })
          .onDuplicateKeyUpdate({
            set: {
              ...rest,
              competitionId: inputCompetition?.id,
            },
          });

        if (inputCompetition) {
          await ctx.db
            .insert(competition)
            .values(inputCompetition)
            .onDuplicateKeyUpdate({
              set: inputCompetition,
            });
        }

        await updateVideoAthletes(ctx.db, isNew, id, athletes);

        await updateVideoTags(ctx.db, isNew, id, tags);
      }

      //if new video uploaded, convert
      if (
        input.fullFilename &&
        newVideo &&
        env.ENABLE_VIDEO_CONVERTER === "true"
      ) {
        await convertVideo({
          videoId: id,
          fullFilename: input.fullFilename,
        });
      }

      return { id, muxPlaybackId: input.muxPlaybackId };
    }),
  updateMany: protectedProcedure
    .input(
      z.array(
        z.object({
          id: z.string(),
          title: z.string().min(1),
          competition: competitionFormSchema.optional(),
          sport: z.enum(videos.sport.enumValues),
          athletes: z.array(
            z.object({
              athleteId: z.string(),
            }),
          ),
          permission: z.enum(videos.permission.enumValues),
          tags: z.array(z.string()),
          videoDate: z.date().optional(),
        }),
      ),
    )
    .mutation(async ({ ctx, input }) => {
      // Check if any of the videos to update are child videos
      const videoIds = input.map((video) => video.id);
      const existingVideos = await ctx.db.query.videos.findMany({
        where: (fields) => inArray(fields.id, videoIds),
        columns: { id: true, parentId: true },
      });

      const childVideos = existingVideos.filter((video) => video.parentId);

      if (childVideos.length > 0) {
        const childVideoIds = childVideos.map((child) => child.id);
        throw new TRPCError({
          code: "FORBIDDEN",
          message: `Cannot edit child videos. The following videos are child videos and inherit their properties from their parent: ${childVideoIds.join(", ")}`,
        });
      }

      const hasCompetitions = input.some((x) => x.competition);
      await updateManyVideos({ db: ctx.db, videosToUpdate: input });
      if (hasCompetitions) {
        await updateManyCompetitions({ db: ctx.db, videosToUpdate: input });
      }
      await updateManyAthletes({ db: ctx.db, videosToUpdate: input });
      await updateManyTags({ db: ctx.db, videosToUpdate: input });
    }),
  generateDownloadUrl: protectedProcedure
    .input(z.object({ videoId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const video = await ctx.db.query.videos.findFirst({
        where: (fields) => eq(fields.id, input.videoId),
        columns: {
          fullFilename: true,
          sport: true,
          permission: true,
          createdById: true,
        },
        with: {
          athletes: true,
        },
      });

      if (!video?.fullFilename || !video.sport) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Video source not found",
        });
      }

      //permission check (for owner/uploader of video)
      const isAdmin = ctx.session.user.roles.includes(UserRole.admin);
      const isAnalyst = ctx.session.user.roles.includes(UserRole.analyst);
      if (!isAdmin && !isAnalyst) {
        const userSports = getUserSports(ctx.session.user.roles as UserRole[]);
        const userHasSportPermission = userSports.includes(video.sport);

        if (!userHasSportPermission) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Permission denied",
          });
        }
      }

      //permission check (for sport and athletes)
      // checkSport(ctx, video.sport);
      // const videoAthletesIds = video.athletes
      //   .filter((x) => x.isHp)
      //   .map((x) => x.athleteId);
      // await checkVideoPermission(ctx, video.permission, videoAthletesIds);

      const cacheUrl = getCloudFrontSignedUrl(video.fullFilename);
      return cacheUrl;
    }),
  delete: protectedProcedure
    .input(z.array(z.string()))
    .mutation(async ({ ctx, input }) => {
      const isAdmin = ctx.session.user.roles.includes("admin");
      const isAnalyst = ctx.session.user.roles.includes("analyst");

      const andCondition = [inArray(videos.id, input)];
      if (!isAdmin) {
        if (isAnalyst) {
          //analyst can view videos in his sports
          const analystSports = getUserSports(
            ctx.session.user.roles as UserRole[],
          );
          if (analystSports.length > 0) {
            andCondition.push(inArray(videos.sport, analystSports));
          } else {
            andCondition.push(eq(videos.createdById, ctx.session.user.id));
          }
        } else {
          andCondition.push(eq(videos.createdById, ctx.session.user.id));
        }
      }

      await ctx.db.transaction(async (tx) => {
        const videosData = await tx.query.videos.findMany({
          where: () => and(...andCondition),
          columns: {
            id: true,
            muxAssetId: true,
            fullFilename: true,
            muxPlaybackId: true,
          },
        });

        //delete db records
        const res = await tx.delete(videos).where(and(...andCondition));

        if (res.rowsAffected === 0) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Video not found / permission denied",
          });
        }
        await tx
          .delete(videoAthletes)
          .where(inArray(videoAthletes.videoId, input));
        await tx.delete(videoTags).where(inArray(videoTags.videoId, input));

        const mux = new Mux({
          tokenId: env.MUX_ACCESS_TOKEN_ID,
          tokenSecret: env.MUX_SECRET_KEY,
        });

        //use try catch because we don't want to stop deleting db records if mux asset or s3 file deletion fails
        //delete mux playback record
        try {
          await Promise.all(
            videosData
              .filter((x) => !!x.muxAssetId && !!x.muxPlaybackId)
              .map(async (v) => {
                return await mux.video.assets.deletePlaybackId(
                  v.muxAssetId!,
                  v.muxPlaybackId!,
                );
              }),
          );
        } catch (e) {
          console.error(e);
        }
        //delete mux asset

        try {
          await Promise.all(
            videosData
              .filter((x) => !!x.muxAssetId)
              .map(async (v) => {
                return await mux.video.assets.delete(v.muxAssetId!);
              }),
          );
        } catch (error) {
          console.error(error);
        }

        //delete s3 files
        try {
          //delete origin source file
          await Promise.all(
            videosData
              .filter((x) => !!x.fullFilename)
              .map(async (v) => {
                const deleteObjectCommand = new DeleteObjectCommand({
                  Bucket: env.AWS_BUCKET_NAME,
                  Key: v.fullFilename!,
                });
                return await s3Client.send(deleteObjectCommand);
              }),
          );

          //delete stream folder
          for (const video of videosData) {
            const monthPath = video.fullFilename
              ?.split("/")
              .slice(0, 2)
              .join("/");
            const folderPath = `${monthPath}/${video.id}/stream/`;

            const listObjectsCommand = new ListObjectsV2Command({
              Bucket: env.AWS_BUCKET_NAME,
              Prefix: folderPath, // Folder prefix
            });

            const listedObjects = await s3Client.send(listObjectsCommand);
            const files = listedObjects.Contents;

            if (files && files.length > 0) {
              const deletePromises = files.map(async (object) => {
                const deleteObjectCommand = new DeleteObjectCommand({
                  Bucket: env.AWS_BUCKET_NAME,
                  Key: object.Key!,
                });

                return await s3Client.send(deleteObjectCommand);
              });

              // Wait for all deletions to finish
              await Promise.all(deletePromises);
            }
          }
        } catch (error) {
          console.error(error);
        }
      });
    }),

  //for hard delete videos & mux assets & s3 files
  cleanVideos: publicProcedure.mutation(async ({ ctx }) => {
    const videosData = await ctx.db.query.videos.findMany({
      where: () => eq(videos.isDeleted, true),
      columns: {
        id: true,
        muxAssetId: true,
        fullFilename: true,
      },
    });

    //delete mux asset
    const mux = new Mux({
      tokenId: env.MUX_ACCESS_TOKEN_ID,
      tokenSecret: env.MUX_SECRET_KEY,
    });

    await Promise.all(
      videosData
        .filter((x) => !!x.muxAssetId)
        .map(async (v) => {
          return await mux.video.assets.delete(v.muxAssetId!);
        }),
    );

    //delete s3 files
    await Promise.all(
      videosData
        .filter((x) => !!x.fullFilename)
        .map(async (v) => {
          const deleteObjectCommand = new DeleteObjectCommand({
            Bucket: env.AWS_BUCKET_NAME,
            Key: v.fullFilename!,
          });
          return await s3Client.send(deleteObjectCommand);
        }),
    );

    //delete db records
    const ids = videosData.map((x) => x.id);
    await ctx.db
      .delete(videoAthletes)
      .where(inArray(videoAthletes.videoId, ids));
    await ctx.db.delete(videoTags).where(inArray(videoTags.videoId, ids));
    await ctx.db.delete(videos).where(eq(videos.isDeleted, true));
  }),

  getVideoSport: protectedProcedure
    .input(z.object({ videoId: z.string() }))
    .query(async ({ ctx, input }) => {
      const video = await ctx.db.query.videos.findFirst({
        where: (fields) => eq(fields.id, input.videoId),
        columns: { sport: true },
      });

      return video?.sport;
    }),

  getVideosBySport: protectedProcedure
    .input(z.object({ sport: z.enum(sports) }))
    .query(async ({ ctx, input }) => {
      const videos = await ctx.db.query.videos.findMany({
        where: (fields) => eq(fields.sport, input.sport),
        columns: {
          id: true,
          title: true,
          filename: true,
        },
        orderBy: (fields) => [fields.title],
      });

      const uniqueVideos = videos.filter(
        (video, index, self) =>
          index === self.findIndex((v) => v.id === video.id),
      );

      return uniqueVideos;
    }),

  createVideoRelationship: protectedProcedure
    .input(
      z.object({
        parentId: z.string(),
        relationships: z.array(
          z.object({
            childId: z.string(),
            relationType: z.string(),
          }),
        ),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { parentId, relationships } = input;

      // Check if parent video exists
      const parentVideo = await ctx.db.query.videos.findFirst({
        where: (fields) => eq(fields.id, parentId),
        columns: { id: true },
      });

      if (!parentVideo) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Parent video not found",
        });
      }

      const childIds = relationships.map((r) => r.childId);

      // Check if all child videos exist
      const childVideos = await ctx.db.query.videos.findMany({
        where: (fields) => inArray(fields.id, childIds),
        columns: { id: true },
      });

      if (childVideos.length !== childIds.length) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "One or more child videos not found",
        });
      }

      // Check for existing relationships (videos that already have this parentId)
      const existingChildVideos = await ctx.db.query.videos.findMany({
        where: (fields) => eq(fields.parentId, parentId),
        columns: { id: true, relationshipType: true },
      });

      const existingChildIds = existingChildVideos.map((v) => v.id);
      const newRelationships = relationships.filter(
        (r) => !existingChildIds.includes(r.childId),
      );

      // Get relationships that need to be updated (existing but with different relation type)
      const relationshipsToUpdate = relationships.filter((r) =>
        existingChildIds.includes(r.childId),
      );

      // Get relationships that need to be removed (existing but not in new list)
      const relationshipsToRemove = existingChildVideos.filter(
        (v) => !childIds.includes(v.id),
      );

      // Get parent video data for inheritance
      const parentVideoData = await ctx.db.query.videos.findFirst({
        where: (fields) => eq(fields.id, parentId),
        columns: {
          id: true,
          sport: true,
          permission: true,
          competitionId: true,
        },
      });

      if (!parentVideoData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Parent video not found",
        });
      }

      // Get parent video's athletes for inheritance
      const parentAthletes = await ctx.db.query.videoAthletes.findMany({
        where: (fields) => eq(fields.videoId, parentId),
        columns: { athleteId: true },
      });

      // Use transaction to update videos table
      await ctx.db.transaction(async (tx) => {
        // Remove relationships that are no longer needed
        if (relationshipsToRemove.length > 0) {
          // Clear parentId for removed relationships
          await tx
            .update(videos)
            .set({
              parentId: null,
              relationshipType: null,
              relationshipCreatedDate: null,
            })
            .where(
              inArray(
                videos.id,
                relationshipsToRemove.map((v) => v.id),
              ),
            );
        }

        // Update existing relationships with new relation types
        for (const relationship of relationshipsToUpdate) {
          await tx
            .update(videos)
            .set({
              relationshipType: relationship.relationType,
              relationshipCreatedDate: sql`CURRENT_TIMESTAMP`,
            })
            .where(eq(videos.id, relationship.childId));
        }

        // Create new relationships
        if (newRelationships.length > 0) {
          // Update each relationship individually to avoid complex SQL
          for (const relationship of newRelationships) {
            await tx
              .update(videos)
              .set({
                parentId,
                relationshipType: relationship.relationType,
                relationshipCreatedDate: sql`CURRENT_TIMESTAMP`,
              })
              .where(eq(videos.id, relationship.childId));
          }
        }

        // Clear and inherit fields for all child videos (both new and updated)
        if (childIds.length > 0) {
          // Clear competition, sport, and permission fields on child videos
          await tx
            .update(videos)
            .set({
              competitionId: parentVideoData.competitionId,
              sport: parentVideoData.sport,
              permission: parentVideoData.permission,
            })
            .where(inArray(videos.id, childIds));

          // Clear existing athletes from child videos
          await tx
            .delete(videoAthletes)
            .where(inArray(videoAthletes.videoId, childIds));

          // Inherit athletes from parent video to child videos
          if (parentAthletes.length > 0) {
            const childAthletes = childIds.flatMap((childId) =>
              parentAthletes.map((athlete) => ({
                videoId: childId,
                athleteId: athlete.athleteId,
              })),
            );

            await tx.insert(videoAthletes).values(childAthletes);
          }
        }
      });

      return {
        success: true,
        created: newRelationships.length,
        updated: relationshipsToUpdate.length,
        removed: relationshipsToRemove.length,
      };
    }),

  removeVideoRelationship: protectedProcedure
    .input(
      z.object({
        parentId: z.string(),
        childId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { parentId, childId } = input;

      // Check if relationship exists
      const existingChildVideo = await ctx.db.query.videos.findFirst({
        where: (fields) =>
          and(eq(fields.parentId, parentId), eq(fields.id, childId)),
        columns: { id: true },
      });

      if (!existingChildVideo) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Relationship not found",
        });
      }

      await ctx.db.transaction(async (tx) => {
        // Clear  relationship fields
        await tx
          .update(videos)
          .set({
            parentId: null,
            relationshipType: null,
            relationshipCreatedDate: null,
          })
          .where(eq(videos.id, childId));
      });

      return { success: true };
    }),

  updateChildVideoMetadata: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        title: z.string().min(1).optional(),
        tags: z.array(z.string()).optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { id, title, tags } = input;

      // Check if the video exists and is child vdeo
      const existingVideo = await ctx.db.query.videos.findFirst({
        where: (fields) => eq(fields.id, id),
        columns: { id: true, parentId: true, sport: true },
      });

      if (!existingVideo) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Video not found",
        });
      }

      if (!existingVideo.parentId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Error updating videos.",
        });
      }

      // Check sport permissions
      if (existingVideo.sport) {
        checkSport(ctx, existingVideo.sport);
      }

      // Use transaction to update only allowed fields
      await ctx.db.transaction(async (tx) => {
        //  titlee
        if (title !== undefined) {
          await tx.update(videos).set({ title }).where(eq(videos.id, id));
        }

        // if (competition) {
        //   await tx.update(videos).set({ competitionId: competition.id });
        // }

        //  tags
        if (tags !== undefined) {
          await updateVideoTags(tx, false, id, tags);
        }
      });

      return { success: true };
    }),

  getLinkedVideos: protectedProcedure
    .input(
      z.object({
        parentId: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { parentId } = input;

      // Check if parent video exists
      const parentVideo = await ctx.db.query.videos.findFirst({
        where: (fields) => eq(fields.id, parentId),
        columns: { id: true },
      });

      if (!parentVideo) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Parent video not found",
        });
      }

      // Get all child videos for this parent
      const childVideos = await ctx.db.query.videos.findMany({
        where: (fields) => eq(fields.parentId, parentId),
        with: {
          athletes: true,
          tags: true,
          competition: true,
        },
        orderBy: (fields) => [fields.relationshipCreatedDate],
      });

      // Process thumbnails and get athlete attributes
      const thumbnailTokensToUpdate: { videoId: string; token: string }[] = [];

      for (const video of childVideos) {
        const thumbnailToken = await refreshMUXToken({
          aud: "thumbnail",
          muxPlaybackId: video.muxPlaybackId!,
          oldToken: video.thumbnailToken,
          thumbnailUrl: video.thumbnail,
        });
        if (thumbnailToken.newToken) {
          thumbnailTokensToUpdate.push({
            videoId: video.id,
            token: thumbnailToken.newToken,
          });
        }

        const token = thumbnailToken.newToken ?? thumbnailToken.oldToken;
        video.thumbnail = `https://image.mux.com/${video.muxPlaybackId}/thumbnail.png?token=${token}`;
      }

      // Get athlete attributes
      const allAthletes = childVideos
        .flatMap((x) =>
          x.athletes?.map((y) => ({
            athleteId: y.athleteId,
            videoId: y.videoId,
          })),
        )
        .filter((x) => !!x);

      const athletesWithAttr = await getAthletesAttr(allAthletes);

      return {
        videoList: childVideos.map((x) => ({
          ...x,
          path: x.fullFilename,
          fullFilename: undefined,
          athletes: x.athletes?.map((athlete) => {
            const athleteWithAttr = athletesWithAttr.find(
              (y) => y.athleteId === athlete.athleteId,
            );
            return {
              ...athlete,
              name: athleteWithAttr?.name ?? "",
              isHp: athleteWithAttr?.isHp ?? false,
            };
          }),
        })),
      };
    }),
});
