import { VideoPermission, type UserRole } from "~/lib/enums";
import type { SortBy, Sport, VideoStatus } from "~/lib/enums";
import { competition, videoAthletes, videoTags, videos } from "../db/schema";
import type {
  Video,
  VideoAthlete,
  VideoCompetition,
  VideoTag,
} from "../db/schema";
import type { Context } from "../api/trpc";
import type { SQLWrapper, SQL } from "drizzle-orm";
import {
  eq,
  inArray,
  sql,
  isNotNull,
  isNull,
  or,
  and,
  like,
  count,
  desc,
  asc,
  notInArray,
} from "drizzle-orm";
import { TRPCError } from "@trpc/server";
import { refreshMUXToken } from "./mux";
import { db } from "../db";
import { getUserSports } from "~/lib/roles";
import { getAthletes, getAthletesAttr, getHpAthleteCached } from "./pta";
import { getServiceToken } from "./keycloak";

export const updateVideosThumbnailTokens = async ({
  db,
  data,
}: {
  db: Context["db"];
  data: { videoId: string; token: string }[];
}) => {
  const sqlChunks: SQL[] = [];
  const ids: string[] = [];

  sqlChunks.push(sql`(case`);

  data.forEach(({ videoId, token }) => {
    sqlChunks.push(sql`when ${videos.id} = ${videoId} then ${token}`);
    ids.push(videoId);
  });
  sqlChunks.push(sql`end)`);

  const finalSql: SQL = sql.join(sqlChunks, sql.raw(" "));

  await db
    .update(videos)
    .set({ thumbnailToken: finalSql })
    .where(inArray(videos.id, ids));
};

export const updateVideoStatus = async ({
  db,
  videoId,
  status,
}: {
  db: Context["db"];
  videoId: string;
  status: VideoStatus;
}) => {
  const response = await db
    .update(videos)
    .set({ status })
    .where(eq(videos.id, videoId));

  if (response.rowsAffected < 1) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "Video not found",
    });
  }

  const video = await db.query.videos.findFirst({
    where: eq(videos.id, videoId),
  });
  return video;
};

export const getVideoByIdForPermissionCheck = async (
  id: string,
  userRoles: UserRole[],
  token: string,
) => {
  const video = await db.query.videos.findFirst({
    where: eq(videos.id, id),
    with: {
      athletes: {
        columns: {
          athleteId: true,
          // isHp: true,
        },
      },
    },
  });

  // if (userRoles.includes(UserRole.admin)) return video;
  const userSports = getUserSports(userRoles);
  const userAthletes = await getAthletes({
    token,
    is_hp: true,
  });

  if (!video?.sport) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "Video sport not found",
    });
  }

  let userHasAthletePerm = false;
  if (video.permission === VideoPermission.public) {
    userHasAthletePerm = true;
  } else {
    const hpAthletes = await getHpAthleteCached();
    const hpAthleteIds = hpAthletes.map((x) => x.athlete_id);

    if (video.athletes.some((x) => !hpAthleteIds.includes(x.athleteId))) {
      userHasAthletePerm = true;
    } else if (userAthletes) {
      userHasAthletePerm = video.athletes.some((x) =>
        userAthletes.find((y) => y.athlete_id === x.athleteId),
      );
    }
  }

  if (!userSports.includes(video.sport) || !userHasAthletePerm) {
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "User does not have permission",
    });
  }

  return video;
};

export const getVideos = async ({
  db,
  isAdmin,
  userSports,
  page,
  pageSize,
  searchText,
  sports,
  videoStatuses,
  metadata,
  subscribedAthletesId,
  isSummary,
  sortBy,
  videoIds, // Add this parameter
}: {
  db: Context["db"];
  isAdmin: boolean;
  userSports?: Sport[];
  page: number;
  pageSize: number;
  searchText?: string;
  sports?: Sport[];
  videoStatuses?: VideoStatus[];
  metadata?: string[];
  subscribedAthletesId?: string[];
  isSummary?: boolean;
  sortBy: SortBy | "name_desc" | "video_date_asc";
  videoIds?: string[];
}) => {
  //base and condition
  const andCondition: (SQLWrapper | undefined)[] = [
    eq(videos.isDeleted, false),
    eq(videos.isDraft, false),
    isNotNull(videos.muxPlaybackId),
  ];

  // If specific video IDs provided, filter by them
  if (videoIds && videoIds.length > 0) {
    andCondition.push(inArray(videos.id, videoIds));
  }

  const hpAthletes = await getHpAthleteCached();
  const hpAthleteIds = hpAthletes.map((x) => x.athlete_id);
  const serviceToken = await getServiceToken();

  //extra condition for non-admin users
  if (!userSports || userSports.length === 0) {
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message:
        "You have no sports assigned to you. Please contact your administrator.",
    });
  }
  andCondition.push(inArray(videos.sport, userSports));

  //get user subscribed athletes

  if (!subscribedAthletesId || subscribedAthletesId.length === 0) {
    andCondition.push(eq(videos.permission, VideoPermission.public));
  } else {
    andCondition.push(
      or(
        eq(videos.permission, VideoPermission.public),
        and(
          inArray(
            videos.id,
            db
              .select({ id: videoAthletes.videoId })
              .from(videoAthletes)
              .where(
                or(
                  inArray(videoAthletes.athleteId, subscribedAthletesId),
                  notInArray(videoAthletes.athleteId, hpAthleteIds),
                ),
              ),
          ),
        ),
      ),
    );
  }

  const offset = page * pageSize;

  //partial search
  //ie. "freestyle 100m" should match videos that contain text/tags "freestyle" and "100m" and not only videos that contain text/tags "freestyle 100m" exactly.
  const searchTextSegments = searchText?.split(" ") ?? [];
  const searchTextOrTitleCondition = searchTextSegments.map((segment) =>
    like(videos.title, `%${segment}%`),
  );
  const searchTextOrSportCondition = searchTextSegments.map((segment) =>
    like(videos.sport, `%${segment}%`),
  );

  //searchtext
  if (searchText !== undefined && searchText.length > 0) {
    const searchedAthletes = await getAthletes({
      token: serviceToken.access_token,
      name: searchText,
    });
    let searchedAthleteIds: string[] = [];
    if (searchedAthletes) {
      searchedAthleteIds = searchedAthletes.map((x) => x.athlete_id);
    }

    const searchTextCondition = [
      ...searchTextOrSportCondition,
      ...searchTextOrTitleCondition,
    ];

    // Add competition search condition only if there are results
    const competitionResults = await db
      .select({ id: competition.id })
      .from(competition)
      .where(like(competition.name, `%${searchText}%`));

    if (competitionResults.length > 0) {
      searchTextCondition.push(
        inArray(
          videos.competitionId,
          db
            .select({ id: competition.id })
            .from(competition)
            .where(like(competition.name, `%${searchText}%`)),
        ),
      );
    }

    // Add tag search condition only if there are results
    const tagResults = await db
      .select({ id: videoTags.videoId })
      .from(videoTags)
      .where(like(videoTags.text, `%${searchText}%`));

    if (tagResults.length > 0) {
      searchTextCondition.push(
        inArray(
          videos.id,
          db
            .select({ id: videoTags.videoId })
            .from(videoTags)
            .where(like(videoTags.text, `%${searchText}%`)),
        ),
      );
    }

    if (searchedAthleteIds.length > 0) {
      // Add athlete search condition only if there are results
      const athleteResults = await db
        .select({ id: videoAthletes.videoId })
        .from(videoAthletes)
        .where(inArray(videoAthletes.athleteId, searchedAthleteIds));

      if (athleteResults.length > 0) {
        searchTextCondition.push(
          inArray(
            videos.id,
            db
              .select({ id: videoAthletes.videoId })
              .from(videoAthletes)
              .where(inArray(videoAthletes.athleteId, searchedAthleteIds)),
          ),
        );
      }
    }

    if (isAdmin) {
      searchTextCondition.push(like(videos.status, `%${searchText}%`));
    }

    // Only add parent search condition if we have search conditions
    if (searchTextCondition.length > 0) {
      // Also include parent videos if their children match the search criteria
      const parentSearchCondition = inArray(
        videos.id,
        db
          .select({ id: videos.parentId })
          .from(videos)
          .where(and(isNotNull(videos.parentId), or(...searchTextCondition))),
      );

      searchTextCondition.push(parentSearchCondition);
    }

    andCondition.push(or(...searchTextCondition));
  }

  // filter by sport
  if (sports !== undefined && sports.length > 0) {
    andCondition.push(inArray(videos.sport, sports));
  }

  // filter by status
  if (videoStatuses !== undefined && videoStatuses.length > 0) {
    andCondition.push(inArray(videos.status, videoStatuses));
  }

  // filter by metadata
  if (metadata !== undefined && metadata.length > 0) {
    // Check if there are any matching tags first
    const matchingTags = await db
      .select({ id: videoTags.videoId })
      .from(videoTags)
      .where(inArray(videoTags.text, metadata));

    if (matchingTags.length > 0) {
      andCondition.push(
        inArray(
          videos.id,
          db
            .select({ id: videoTags.videoId })
            .from(videoTags)
            .where(inArray(videoTags.text, metadata)),
        ),
      );
    }
  }

  const where = and(...andCondition);

  // Filter to only show parent videos (videos without parentId) or videos that don't have any parent
  const parentVideosWhere = and(
    where,
    isNull(videos.parentId), // Only show videos without parentId (parent videos)
  );

  const videosCountResponse = await db
    .select({ count: count() })
    .from(videos)
    .where(parentVideosWhere);
  const videosCount = videosCountResponse[0]?.count;

  interface VideoWithDetail extends Video {
    athletes: VideoAthlete[];
    tags: VideoTag[];
    competition: VideoCompetition;
    relationType: string;
    linkedVideoCount?: number | null; // Add linked video count
  }

  const sortByMap = {
    name: [asc(videos.title), (desc(videos.videoDate), desc(videos.createdAt))], //sort by title first, then videoDate
    name_desc: [
      desc(videos.title),
      (desc(videos.videoDate), desc(videos.createdAt)),
    ], //sort by title first, then videoDate
    video_date: (desc(videos.videoDate), desc(videos.createdAt)), //sort by videoDate, if null, sort by createdAt
    video_date_asc: (asc(videos.videoDate), asc(videos.createdAt)), //sort by videoDate, if null, sort by createdAt
  };

  // Get parent videos only (videos without parentId)
  const videoList = (await db.query.videos.findMany({
    where: parentVideosWhere,
    //this is where to filter out child videos, and return the parent videos with COUNT of children
    //if isSummary, only return the summary fields
    columns: isSummary
      ? {
          id: true,
          title: true,
          sport: true,
          permission: true,
          status: true,
          duration: true,
          fps: true,
          isDraft: true,
          isDeleted: true,
          createdAt: true,
          updatedAt: true,
          fullFilename: true,
        }
      : undefined,
    //if isSummary, only return the summary fields
    with: isSummary
      ? undefined
      : {
          athletes: true,
          tags: true,
          competition: true,
        },
    orderBy: sortByMap[sortBy],
    offset,
    limit: pageSize,
  })) as VideoWithDetail[];

  // Get linked video counts for each parent video
  let linkedVideoCounts: { parentId: string | null; count: number }[] = [];

  if (videoList.length > 0) {
    linkedVideoCounts = await db
      .select({
        parentId: videos.parentId,
        count: count(),
      })
      .from(videos)
      .where(
        and(
          isNotNull(videos.parentId),
          inArray(
            videos.parentId,
            videoList.map((v) => v.id),
          ),
        ),
      )
      .groupBy(videos.parentId);
  }

  // Create a map of parent ID to linked video count
  const linkedVideoCountMap = new Map<string, number>();
  linkedVideoCounts.forEach((item) => {
    if (item.parentId) {
      linkedVideoCountMap.set(item.parentId, Number(item.count));
    }
  });

  // Add linked video count to each video
  videoList.forEach((video) => {
    video.linkedVideoCount = linkedVideoCountMap.get(video.id) ?? null;
  });

  if (!isSummary) {
    const thumbnailTokensToUpdate: { videoId: string; token: string }[] = [];

    for (const video of videoList) {
      const thumbnailToken = await refreshMUXToken({
        aud: "thumbnail",
        muxPlaybackId: video.muxPlaybackId!,
        oldToken: video.thumbnailToken,
        thumbnailUrl: video.thumbnail,
      });
      if (thumbnailToken.newToken) {
        thumbnailTokensToUpdate.push({
          videoId: video.id,
          token: thumbnailToken.newToken,
        });
      }

      const token = thumbnailToken.newToken ?? thumbnailToken.oldToken;
      video.thumbnail = `https://image.mux.com/${video.muxPlaybackId}/thumbnail.png?token=${token}`;
    }
    //update tokens
    if (thumbnailTokensToUpdate.length > 0) {
      await updateVideosThumbnailTokens({
        db,
        data: thumbnailTokensToUpdate,
      });
    }
  }

  // console.log("start get all athletes", videoList[0]);
  const allAthletes = videoList
    .flatMap((x) =>
      x.athletes?.map((y) => ({ athleteId: y.athleteId, videoId: y.videoId })),
    )
    .filter((x) => !!x);

  const athletesWithAttr = await getAthletesAttr(allAthletes);

  return {
    videosCount,
    videoList: videoList.map((x) => ({
      ...x,
      path: x.fullFilename,
      fullFilename: undefined,
      athletes: x.athletes?.map((athlete) => {
        const athleteWithAttr = athletesWithAttr.find(
          (y) => y.athleteId === athlete.athleteId,
        );
        return {
          ...athlete,
          name: athleteWithAttr?.name ?? "",
          isHp: athleteWithAttr?.isHp ?? false,
        };
      }),
    })),
  };
};

export const updateVideoAthletes = async (
  db: Context["db"],
  isNewVideo: boolean,
  videoId: string,
  athletes?: { athleteId: string }[],
) => {
  if (!isNewVideo && athletes !== undefined) {
    await db.delete(videoAthletes).where(eq(videoAthletes.videoId, videoId));
  }
  if (athletes && athletes.length > 0) {
    await db.insert(videoAthletes).values(
      athletes.map(({ athleteId }) => ({
        athleteId,
        videoId,
      })),
    );
  }
};

export const updateVideoTags = async (
  db: Context["db"],
  isNewVideo: boolean,
  videoId: string,
  tags?: string[],
) => {
  if (!isNewVideo && tags !== undefined) {
    await db.delete(videoTags).where(eq(videoTags.videoId, videoId));
  }
  if (tags && tags.length > 0) {
    await db.insert(videoTags).values(
      tags.map((text) => ({
        text,
        videoId,
      })),
    );
  }
};

export const updateManyVideos = async ({
  db,
  videosToUpdate,
}: {
  db: Context["db"];
  videosToUpdate: {
    id: string;
    title: string;
    sport: Sport;
    permission: VideoPermission;
    videoDate?: Date | null;
  }[];
}) => {
  const ids: string[] = [];
  const sqlChunksTitle: SQL[] = [sql`(case`];
  const sqlChunksSport: SQL[] = [sql`(case`];
  const sqlChunksPermission: SQL[] = [sql`(case`];
  const sqlChunksVideoDate: SQL[] = [sql`(case`];

  for (const input of videosToUpdate) {
    sqlChunksTitle.push(
      sql`when ${videos.id} = ${input.id} then ${input.title}`,
    );
    sqlChunksSport.push(
      sql`when ${videos.id} = ${input.id} then ${input.sport}`,
    );
    sqlChunksPermission.push(
      sql`when ${videos.id} = ${input.id} then ${input.permission}`,
    );
    if (input.videoDate) {
      sqlChunksVideoDate.push(
        sql`when ${videos.id} = ${input.id} then ${input.videoDate}`,
      );
    }
    ids.push(input.id);
  }
  sqlChunksTitle.push(sql`end)`);
  sqlChunksSport.push(sql`end)`);
  sqlChunksPermission.push(sql`end)`);
  sqlChunksVideoDate.push(sql`end)`);
  const finalSqlTitle: SQL = sql.join(sqlChunksTitle, sql.raw(" "));
  const finalSqlSport: SQL = sql.join(sqlChunksSport, sql.raw(" "));
  const finalSqlPermission: SQL = sql.join(sqlChunksPermission, sql.raw(" "));
  const finalSqlVideoDate: SQL = sql.join(sqlChunksVideoDate, sql.raw(" "));

  await db
    .update(videos)
    .set({
      title: finalSqlTitle,
      sport: finalSqlSport,
      permission: finalSqlPermission,
      videoDate: finalSqlVideoDate,
    })
    .where(inArray(videos.id, ids));
};

export const updateManyCompetitions = async ({
  db,
  videosToUpdate,
}: {
  db: Context["db"];
  videosToUpdate: {
    id: string;
    competition?: {
      id: string;
      name: string;
      type?: string | null;
      date?: string | null;
      location?: string | null;
      sport: Sport;
      isOfficial?: boolean | null;
    };
  }[];
}) => {
  //create competitions which are not in db
  await db
    .insert(competition)
    .values(
      videosToUpdate
        .filter((x) => !!x.competition)
        .map(({ competition }) => competition!),
    )
    .onDuplicateKeyUpdate({
      set: {
        name: sql`values(${competition.name})`,
        type: sql`values(${competition.type})`,
        date: sql`values(${competition.date})`,
        location: sql`values(${competition.location})`,
        sport: sql`values(${competition.sport})`,
        isOfficial: sql`values(${competition.isOfficial})`,
      },
    });

  //update videos with competition id
  const ids: string[] = [];
  const sqlChunks: SQL[] = [];

  sqlChunks.push(sql`(case`);

  for (const { id, competition } of videosToUpdate) {
    sqlChunks.push(
      sql`when ${videos.id} = ${id} then ${competition?.id ?? null}`,
    );
    ids.push(id);
  }
  sqlChunks.push(sql`end)`);
  const finalSql: SQL = sql.join(sqlChunks, sql.raw(" "));
  await db
    .update(videos)
    .set({ competitionId: finalSql })
    .where(inArray(videos.id, ids));
};

export const updateManyAthletes = async ({
  db,
  videosToUpdate,
}: {
  db: Context["db"];
  videosToUpdate: {
    id: string;
    athletes: {
      athleteId: string;
    }[];
  }[];
}) => {
  const videoIds = videosToUpdate.map((x) => x.id);
  await db
    .delete(videoAthletes)
    .where(inArray(videoAthletes.videoId, videoIds));

  const athletesToCreate = videosToUpdate.flatMap(({ id, athletes }) =>
    athletes.map((athlete) => ({ ...athlete, videoId: id })),
  );
  if (athletesToCreate.length > 0) {
    await db.insert(videoAthletes).values(athletesToCreate);
  }
};

export const updateManyTags = async ({
  db,
  videosToUpdate,
}: {
  db: Context["db"];
  videosToUpdate: {
    id: string;
    tags: string[];
  }[];
}) => {
  const videoIds = videosToUpdate.map((x) => x.id);
  await db.delete(videoTags).where(inArray(videoTags.videoId, videoIds));

  const tagsToCreate = videosToUpdate.flatMap(({ id, tags }) =>
    tags.map((text) => ({ text, videoId: id })),
  );

  if (tagsToCreate.length > 0) {
    await db.insert(videoTags).values(tagsToCreate);
  }
};
