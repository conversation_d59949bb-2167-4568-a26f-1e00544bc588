import { MUXPlayer } from "~/app/_components/MUXPlayer";
import { api } from "~/trpc/server";
import { ErrorBox } from "~/app/_components/ErrorBox";
import { Tag } from "~/app/_components/Tag";
import { TRPCError } from "@trpc/server";
import { BackButton } from "~/app/_components/BackButton";
import { LinkedVideosList } from "~/app/_components/LinkedVideosList";

//force update if there are data changes in db
export const dynamic = "force-dynamic";

export default async function Home({ params }: { params: { vid: string } }) {
  try {
    const video = await api.video.get({
      id: params.vid,
    });

    // Check if this video has children (is a parent)
    const linkedVideosData = await api.video.getLinkedVideos({
      parentId: params.vid,
    });

    const linkedVideos = linkedVideosData.videoList;
    const isParent = linkedVideos.length > 0;

    const athletes = video.athletes;
    return (
      <main className="grid w-full justify-center gap-4 md:h-full md:w-full md:gap-6 md:p-4 md:px-12 lg:grid-cols-3 lg:gap-10">
        {(!video.muxAssetId || !video.muxPlaybackId) && (
          <ErrorBox code="404" message="Video not found" />
        )}
        {video.muxAssetId && video.muxPlaybackId && (
          <>
            {/* Video and info - takes 2 columns */}
            <div className="flex flex-col justify-center gap-3 lg:col-span-2">
              <BackButton />
              <MUXPlayer
                thumbnailToken={video.thumbnailToken ?? ""}
                playbackToken={video.playbackToken ?? ""}
                storyboardToken={video.storyboardToken ?? ""}
                playbackId={video.muxPlaybackId}
              />
              <div className="my-4 flex flex-col gap-4 px-3 text-white">
                <p className="font-bold capitalize md:text-2xl">
                  {video.title ?? ""}
                </p>
                <div className="flex flex-row gap-2">
                  {video.tags.map((tag) => (
                    <div key={tag.text}>
                      <Tag label={tag.text} />
                    </div>
                  ))}
                  {isParent && (
                    <div>
                      <Tag
                        label="Parent"
                        className="border-blue-400 text-blue-400"
                      />
                    </div>
                  )}
                </div>
                <div className="flex flex-col gap-2 text-white">
                  <p>Sport: {video.sport}</p>
                  <p>status: {video.status}</p>
                  {athletes && athletes.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      <p className="font-bold">Athlete:&nbsp;</p>
                      {athletes.map((athlete) => athlete.name).join(", ")}
                    </div>
                  )}
                </div>
                {video.competition && (
                  <div className="text-white">
                    <p className="font-bold">
                      Competition: {video.competition.date ?? ""}
                    </p>
                    <p>{video.competition?.name}</p>
                    <p>{video.competition?.location}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Linked Videos List - takes 1 column */}
            <div className="lg:col-span-1">
              <LinkedVideosList
                currentVideoId={video.id}
                currentVideoTitle={video.title ? video.title : ""}
                currentVideoThumbnail={video.thumbnail ?? ""}
                currentVideoPlaybackId={video.muxPlaybackId ?? ""}
              />
            </div>
          </>
        )}
      </main>
    );
  } catch (error) {
    if (error instanceof TRPCError) {
      return <ErrorBox code={error.code} message={error.message} />;
    } else {
      return <div>An unexpected error occurred</div>;
    }
  }
}
